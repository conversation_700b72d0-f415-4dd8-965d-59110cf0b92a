# 机构树选中逻辑修改说明

## 修改内容

已成功修改了角色编辑弹窗中的机构树选中逻辑，现在会将**选中的节点**和**半选中的节点**都传递给接口，同时保持用户操作的自然性。

## 主要修改

### 1. 保持 `handleMenuCheck` 函数的简洁性
- 直接使用用户选择的节点，保持操作的自然性
- 用户可以自由选择和取消选择节点，不会被强制选中

### 2. 新增 `getCompletePermissionData` 函数
- 专门用于获取完整的权限数据（选中+半选中）
- 使用 `menuTreeRef.value.getCheckedData()` 获取完全选中的节点
- 使用 `menuTreeRef.value.getIndeterminateData()` 获取半选中的节点
- 将两种状态的节点ID合并并去重

### 3. 修改了 `handleSave` 函数
- 在提交前调用 `getCompletePermissionData()` 获取完整权限数据
- 创建新的提交数据对象，不修改原始的 `formData.menus`
- 通过 `emit('save', submitData)` 将完整权限数据传递给父组件
- 确保用户的选择状态不会被意外修改

### 4. 修改了父组件 `RolesPage.js` 的 `handleSave` 函数
- 接收子组件传递的完整权限数据
- 支持向后兼容，如果没有传递数据则使用原有逻辑

### 5. 新增 `getActualUserSelection` 函数
- 从完整权限数据中推导出用户实际选择的节点
- 解决编辑时权限树显示不正确的问题
- 移除因级联选择而自动包含的冗余节点

### 6. 优化了 `checkAll` 函数
- 修改为选中所有节点（包括父节点和子节点）
- 移除了未使用的 `getAllLeafKeys` 函数

### 7. 添加了权限数据处理的监听器
- 监听弹窗显示状态，在打开时处理权限数据
- 监听菜单树变化，确保权限数据正确渲染

## 数据流程

1. **编辑时** → 后端返回完整权限数据（包含选中+半选中的节点）
2. **权限数据优化** → 调用 `getActualUserSelection()` 推导出用户实际选择的节点
3. **渲染树状态** → 根据优化后的权限数据正确渲染树的选中状态
4. **用户操作树节点** → `handleMenuCheck` 被触发 → 直接更新用户选择到 `formData.menus`
5. **用户继续操作** → 可以自由选择/取消选择节点，不会被强制选中
6. **点击保存** → `handleSave` 被触发
7. **获取完整数据** → 调用 `getCompletePermissionData()` 获取选中+半选中的节点
8. **创建提交数据** → 创建新的数据对象，不修改原始 `formData`
9. **传递给父组件** → 通过 `emit('save', submitData)` 传递完整权限数据
10. **父组件处理** → 接收完整权限数据并提交给API

## 控制台日志

修改后会在控制台输出详细的调试信息：
- **权限优化过程**：显示原始权限数据和优化后的用户实际选择
- **编辑时的数据处理**：显示从后端获取的权限数据和推导出的用户选择
- **获取完整权限数据**：显示选中节点、半选中节点和最终合并结果
- **最终提交数据**：显示提交给API的完整权限数据

## 测试方法

### 新增角色测试
1. 打开角色管理页面，点击"新增角色"
2. 在权限树中选择部分子节点（观察父节点变为半选中状态）
3. 点击保存，查看控制台输出，确认选中和半选中的节点都被包含
4. 验证后端接收到的数据包含完整的权限信息

### 编辑角色测试（重点）
1. 新增一个角色并保存（选择一些权限）
2. 编辑刚才创建的角色
3. 确认权限树正确显示之前选择的权限状态（不会显示多余的选中项）
4. 修改一些权限选择（添加或删除）
5. 保存后再次编辑，确认显示最新的权限状态
6. 查看控制台的权限优化过程日志

### 级联选择测试
1. 选择一个父节点的所有子节点
2. 观察父节点自动变为选中状态
3. 保存后编辑，确认显示为选中父节点而不是所有子节点

## 兼容性

- 添加了错误处理，如果 `getCheckedData()` 或 `getIndeterminateData()` 方法不可用，会回退到原来的逻辑
- 保持了与现有API接口的兼容性
- 不影响其他功能的正常使用

## 文件修改

- `src/views/system/components/RoleEditModal.js` - 主要修改文件

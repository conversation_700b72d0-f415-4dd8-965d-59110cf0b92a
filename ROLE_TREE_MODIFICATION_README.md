# 机构树选中逻辑修改说明

## 修改内容

已成功修改了角色编辑弹窗中的机构树选中逻辑，现在会将**选中的节点**和**半选中的节点**都传递给接口。

## 主要修改

### 1. 修改了 `handleMenuCheck` 函数
- 使用 `menuTreeRef.value.getCheckedData()` 获取完全选中的节点
- 使用 `menuTreeRef.value.getIndeterminateData()` 获取半选中的节点
- 将两种状态的节点ID合并并去重后传递给 `formData.menus`

### 2. 修改了 `handleSave` 函数
- 在保存前再次获取最新的选中和半选中节点数据
- 确保传递给后端的权限数据包含所有相关节点

### 3. 优化了 `checkAll` 函数
- 修改为选中所有节点（包括父节点和子节点）
- 移除了未使用的 `getAllLeafKeys` 函数

## 数据流程

1. **用户操作树节点** → `handleMenuCheck` 被触发
2. **获取选中数据** → 调用 `getCheckedData()` 和 `getIndeterminateData()`
3. **合并数据** → 将选中和半选中的节点ID合并
4. **更新表单** → 更新 `formData.menus` 字段
5. **保存时验证** → 在 `handleSave` 中再次获取最新数据

## 控制台日志

修改后会在控制台输出详细的调试信息：
- 选中的节点ID列表
- 半选中的节点ID列表  
- 合并后的最终权限列表

## 测试方法

1. 打开角色管理页面
2. 点击"新增角色"或"编辑角色"
3. 在权限树中选择部分子节点（会产生半选中的父节点）
4. 查看控制台输出，确认选中和半选中的节点都被包含
5. 保存角色，验证传递给后端的数据包含所有相关节点

## 兼容性

- 添加了错误处理，如果 `getCheckedData()` 或 `getIndeterminateData()` 方法不可用，会回退到原来的逻辑
- 保持了与现有API接口的兼容性
- 不影响其他功能的正常使用

## 文件修改

- `src/views/system/components/RoleEditModal.js` - 主要修改文件

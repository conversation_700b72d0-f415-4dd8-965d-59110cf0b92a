# 机构树选中逻辑修改说明

## 修改内容

已成功修改了角色编辑弹窗中的机构树选中逻辑，现在会将**选中的节点**和**半选中的节点**都传递给接口，同时保持用户操作的自然性。

## 主要修改

### 1. 保持 `handleMenuCheck` 函数的简洁性
- 直接使用用户选择的节点，保持操作的自然性
- 用户可以自由选择和取消选择节点，不会被强制选中

### 2. 新增 `getCompletePermissionData` 函数
- 专门用于获取完整的权限数据（选中+半选中）
- 使用 `menuTreeRef.value.getCheckedData()` 获取完全选中的节点
- 使用 `menuTreeRef.value.getIndeterminateData()` 获取半选中的节点
- 将两种状态的节点ID合并并去重

### 3. 修改了 `handleSave` 函数
- 在提交前调用 `getCompletePermissionData()` 获取完整权限数据
- 只在最终提交时将选中和半选中的节点都传递给接口
- 确保传递给后端的权限数据包含所有相关节点

### 4. 优化了 `checkAll` 函数
- 修改为选中所有节点（包括父节点和子节点）
- 移除了未使用的 `getAllLeafKeys` 函数

## 数据流程

1. **用户操作树节点** → `handleMenuCheck` 被触发 → 直接更新用户选择
2. **用户继续操作** → 可以自由选择/取消选择节点
3. **点击保存** → `handleSave` 被触发
4. **获取完整数据** → 调用 `getCompletePermissionData()`
5. **合并权限** → 将选中和半选中的节点ID合并
6. **提交数据** → 将完整权限数据传递给后端

## 控制台日志

修改后会在控制台输出详细的调试信息：
- 获取完整权限数据时的选中节点ID列表
- 获取完整权限数据时的半选中节点ID列表
- 最终提交给接口的完整权限列表

## 测试方法

1. 打开角色管理页面
2. 点击"新增角色"或"编辑角色"
3. 在权限树中选择部分子节点（会产生半选中的父节点）
4. 查看控制台输出，确认选中和半选中的节点都被包含
5. 保存角色，验证传递给后端的数据包含所有相关节点

## 兼容性

- 添加了错误处理，如果 `getCheckedData()` 或 `getIndeterminateData()` 方法不可用，会回退到原来的逻辑
- 保持了与现有API接口的兼容性
- 不影响其他功能的正常使用

## 文件修改

- `src/views/system/components/RoleEditModal.js` - 主要修改文件

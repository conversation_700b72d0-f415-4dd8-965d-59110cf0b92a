import { ref, watch, nextTick } from 'vue';
import messages from '@/utils/messages';

export function useRoleEditModal(props, emit) {
  // 响应式数据
  const formRef = ref(null);
  const menuTreeRef = ref(null);
  const expandedKeys = ref([]);

  // 表单验证规则
  const rules = {
    roleName: [
      {
        required: true,
        message: '请输入角色名称（2-20个汉字）',
        trigger: ['blur', 'input'],
        validator: (_rule, value) => {
          if (!value) {
            return new Error('角色名称不能为空');
          } else if (value.length < 2 || value.length > 20) {
            return new Error('角色名称长度应在2-20个字符之间');
          }
          return true;
        }
      }
    ],
    roleCode: [
      {
        required: true,
        message: '请输入角色代码（2-20个大写字母或下划线）',
        trigger: ['blur', 'input'],
        validator: (_rule, value) => {
          if (!value || value.trim() === '') {
            return new Error('角色代码不能为空');
          } else if (!/^[a-zA-Z_]{2,20}$/.test(value)) {
            return new Error('角色代码应为2-20个大写字母或下划线');
          }
          return true;
        }
      }
    ],
    menus: [
      {
        type: 'array',
        required: true,
        message: '请至少选择一个菜单权限',
        trigger: 'change',
        validator: (_rule, value) => {
          if (!value || value.length === 0) {
            return new Error('请至少选择一个菜单权限');
          }
          return true;
        }
      }
    ]
  };

  // 获取所有节点的key（用于全部展开/收起）
  const getAllNodeKeys = (nodes) => {
    const keys = [];
    const traverse = (nodeList) => {
      nodeList.forEach(node => {
        keys.push(node.id);
        if (node.subMenus && node.subMenus.length > 0) {
          traverse(node.subMenus);
        }
      });
    };
    traverse(nodes);
    return keys;
  };

  // 从完整权限数据中推导出用户实际选择的节点
  // 原理：移除那些因为级联选择而自动包含的父节点，只保留用户真正选择的节点
  const getActualUserSelection = (allPermissions, menuTree) => {
    if (!allPermissions || !menuTree || allPermissions.length === 0) {
      return [];
    }

    const permissionSet = new Set(allPermissions);
    const result = [...allPermissions]; // 从所有权限开始

    // 递归检查每个节点，如果一个父节点的所有子节点都被选中，则移除子节点，保留父节点
    const optimizeSelection = (nodes) => {
      nodes.forEach(node => {
        if (node.subMenus && node.subMenus.length > 0) {
          // 获取所有直接子节点ID
          const directChildIds = node.subMenus.map(child => child.id);

          // 检查所有直接子节点是否都在权限列表中
          const allDirectChildrenSelected = directChildIds.every(id => permissionSet.has(id));

          if (allDirectChildrenSelected && permissionSet.has(node.id)) {
            // 如果父节点和所有直接子节点都被选中，移除子节点，保留父节点
            directChildIds.forEach(childId => {
              const index = result.indexOf(childId);
              if (index > -1) {
                result.splice(index, 1);
              }
            });
          }

          // 递归处理子节点
          optimizeSelection(node.subMenus);
        }
      });
    };

    optimizeSelection(menuTree);

    console.log('权限优化过程:', {
      original: allPermissions,
      optimized: result
    });

    return result;
  };

  // 处理菜单选择
  const handleMenuCheck = (checkedKeys) => {
    // 直接使用用户选择的节点，保持操作的自然性
    props.formData.menus = checkedKeys;

    // 触发表单验证
    if (formRef.value) {
      formRef.value.validate()
        .then(() => {
          // 验证成功
        })
        .catch((errors) => {
          // 验证失败，处理错误
          console.error('表单验证失败:', errors);
        });
    }
  };

  // 处理展开状态变化
  const handleExpandedKeysChange = (keys) => {
    expandedKeys.value = keys;
  };

  // 全部展开
  const expandAll = () => {
    if (props.menuTree && props.menuTree.length > 0) {
      expandedKeys.value = getAllNodeKeys(props.menuTree);
    }
  };

  // 全部收起
  const collapseAll = () => {
    expandedKeys.value = [];
  };

  // 全选
  const checkAll = () => {
    if (props.menuTree && props.menuTree.length > 0) {
      const allKeys = getAllNodeKeys(props.menuTree);
      props.formData.menus = allKeys;
      // 选中所有节点（包括父节点和子节点）
    }
  };

  // 取消全选
  const uncheckAll = () => {
    props.formData.menus = [];
  };

  // 获取完整的权限数据（选中+半选中）
  const getCompletePermissionData = () => {
    if (!menuTreeRef.value) {
      return props.formData.menus || [];
    }

    try {
      // 获取完全选中的节点
      const checkedData = menuTreeRef.value.getCheckedData();
      // 获取半选中的节点
      const indeterminateData = menuTreeRef.value.getIndeterminateData();

      // 合并选中和半选中的节点ID
      const allSelectedKeys = [
        ...(checkedData.keys || []),
        ...(indeterminateData.keys || [])
      ];

      // 去重并返回
      const finalPermissions = [...new Set(allSelectedKeys)];

      console.log('获取完整权限数据:', {
        checked: checkedData.keys,
        indeterminate: indeterminateData.keys,
        final: finalPermissions
      });

      return finalPermissions;
    } catch (error) {
      console.warn('获取权限数据失败，使用默认数据:', error);
      return props.formData.menus || [];
    }
  };

  // 保存
  const handleSave = async () => {
    if (!formRef.value) return;

    try {
      // 验证表单
      await formRef.value.validate();

      // 确保 roleCode 字段不为空
      if (!props.formData.roleCode || props.formData.roleCode.trim() === '') {
        messages.error('角色代码不能为空');
        return;
      }

      // 获取完整的权限数据（包含选中和半选中的节点）
      const completePermissions = getCompletePermissionData();

      // 确保至少选择了一个菜单权限
      if (!completePermissions || completePermissions.length === 0) {
        messages.error('请至少选择一个菜单权限');
        return;
      }

      console.log('用户选择的权限:', props.formData.menus);
      console.log('提交给接口的完整权限数据:', completePermissions);

      // 创建提交数据，不修改原始的formData
      const submitData = {
        ...props.formData,
        menus: completePermissions
      };

      // 触发保存事件，传递完整的权限数据
      emit('save', submitData);
    } catch (errors) {
      console.error('表单验证失败:', errors);
      messages.error('请检查表单填写是否正确');
    }
  };

  // 取消
  const handleCancel = () => {
    emit('cancel');
  };

  // 监听弹窗显示状态，初始化展开状态和权限数据
  watch(() => props.visible, (newValue) => {
    if (newValue && props.menuTree && props.menuTree.length > 0) {
      nextTick(() => {
        expandAll();
        // 当弹窗打开时，处理编辑模式下的权限数据
        if (props.formData.menus && props.formData.menus.length > 0) {
          const actualSelection = getActualUserSelection(props.formData.menus, props.menuTree);
          console.log('原始权限数据:', props.formData.menus);
          console.log('推导出的用户实际选择:', actualSelection);
          // 更新为用户实际选择的节点
          props.formData.menus = actualSelection;
        }
      });
    }
  });

  // 监听菜单树变化，更新展开状态
  watch(() => props.menuTree, (newValue) => {
    if (newValue && newValue.length > 0 && props.visible) {
      nextTick(() => {
        expandAll();
        // 当菜单树数据更新时，也需要处理权限数据
        if (props.formData.menus && props.formData.menus.length > 0) {
          const actualSelection = getActualUserSelection(props.formData.menus, newValue);
          console.log('菜单树更新后，推导出的用户实际选择:', actualSelection);
          props.formData.menus = actualSelection;
        }
      });
    }
  }, { deep: true });

  return {
    formRef,
    menuTreeRef,
    rules,
    expandedKeys,
    handleMenuCheck,
    handleExpandedKeysChange,
    expandAll,
    collapseAll,
    checkAll,
    uncheckAll,
    handleSave,
    handleCancel
  };
}
